// src/events/messageReactionAdd.js
import { Events } from 'discord.js';
import { getConfig } from '@core/config.js';

export const event = {
  name: Events.MessageReactionAdd,
  once: false,
};

export const action = async (reaction, user) => {
  if (user.bot) return;

  if (reaction.partial) {
    try {
      await reaction.fetch();
    } catch (err) {
      console.error('❌ 無法 fetch reaction:', err);
      return;
    }
  }

  // 從 config.yml 獲取配置
  const config = getConfig();

  // 檢查配置是否存在
  if (!config.ROLE?.ID_1) {
    console.error('❌ config.yml 中找不到 ROLE.ID_1 配置');
    return;
  }

  if (!config.MESSAGE?.ID_1) {
    console.error('❌ config.yml 中找不到 MESSAGE.ID_1 配置');
    return;
  }

  // 使用配置文件中的 ID
  const roleId = config.ROLE.ID_1;
  const messageId = config.MESSAGE.ID_1;
  const reactionEmoji = config.EMOJI?.REACTION_1 || '✅';

  // 檢查是否為指定的訊息和表情符號
  if (reaction.message.id !== messageId) return;
  if (reaction.emoji.name !== reactionEmoji) return;

  const guild = reaction.message.guild;
  const member = await guild.members.fetch(user.id);

  if (!member.roles.cache.has(roleId)) {
    await member.roles.add(roleId);
    console.log(`✅ 已為 ${user.tag} 加上角色 (ID: ${roleId})`);
  }
};
