import axios from 'axios';

export class DiscordAuth {
    constructor() {
        this.clientId = process.env.VITE_DISCORD_CLIENT_ID || '746358667531649095';
        this.clientSecret = process.env.DISCORD_CLIENT_SECRET;
        this.redirectUri = process.env.DISCORD_REDIRECT_URI;

        // 調試信息
        console.log('Discord Auth 配置:');
        console.log('Client ID:', this.clientId);
        console.log('Redirect URI:', this.redirectUri);
    }

    // 處理 OAuth2 回調，交換 code 為 access token
    async handleCallback(code) {
        try {
            console.log('處理 Discord OAuth2 回調，code:', code);

            const params = {
                client_id: this.clientId,
                client_secret: this.clientSecret,
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: this.redirectUri,
                scope: 'identify guilds'
            };

            console.log('Token 請求參數:', params);

            const tokenResponse = await axios.post('https://discord.com/api/oauth2/token',
                new URLSearchParams(params),
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    }
                }
            );

            console.log('Token 響應成功:', tokenResponse.data);
            return tokenResponse.data;
        } catch (error) {
            console.error('Discord OAuth2 錯誤:', error.response?.data || error.message);
            console.error('錯誤狀態:', error.response?.status);
            console.error('錯誤詳情:', error.response?.data);
            throw new Error('Discord 認證失敗: ' + (error.response?.data?.error_description || error.message));
        }
    }

    // 獲取用戶資訊
    async getUserInfo(accessToken) {
        try {
            const response = await axios.get('https://discord.com/api/users/@me', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });
            return response.data;
        } catch (error) {
            console.error('獲取用戶資訊錯誤:', error.response?.data || error.message);
            throw new Error('無法獲取用戶資訊');
        }
    }

    // 獲取用戶的伺服器列表
    async getUserGuilds(accessToken) {
        try {
            const response = await axios.get('https://discord.com/api/users/@me/guilds', {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });
            return response.data;
        } catch (error) {
            console.error('獲取伺服器列表錯誤:', error.response?.data || error.message);
            throw new Error('無法獲取伺服器列表');
        }
    }
}
