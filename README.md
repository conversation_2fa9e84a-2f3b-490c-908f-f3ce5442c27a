# 🤖 Discord Bot with discord.js

這是一個使用 [discord.js v14](https://discord.js.org/) 框架開發的 Discord 機器人，具備基本指令架構與啟動功能，可依需求擴充更多功能。

## 📦 功能

=======

- [✅] /ping 指令
- [✅] 指令註冊自動化

### 🚧 待開發功能

## 🔧 安裝步驟

1. 安裝 Node.js（需版本 v22 或以上）  
   👉 [Node.js 官方網站](https://nodejs.org/)

2. 安裝 [Yarn](https://yarnpkg.com/)

3. 可選：安裝 [nvm-windows](https://github.com/coreybutler/nvm-windows) 管理 Node 版本

4. 安裝相依套件：

```bash
yarn add discord.js fast-glob vite-node dotenv pinia vue
```

## 🧪 測試與執行

使用以下指令啟動 Bot：

```bash
yarn dev
```

├─ src/
│  ├─ commands/
│  │  ├─ role/
│  │  ├─ ping/  
│  │  └─ help/
│  ├─ events/
│  │  ├─ interactionCreate/
│  │  │  └ index.js
│  │  ├─ ready/
│  │  │  └─ index.js  
│  │  └─ role/
│  │     └─ index.js
│  ├─ core/
│  │  ├─ loader.js
│  │  └─ vue.js
│  ├─ store/
│  │  └─ app.js
│  ├─ utils/
│  │  └─ logger.js
│  ├─ web/
│  │  ├─ app.vue
│  │  ├─ Callback.vue
│  │  ├─ index.html
│  │  ├─ main.js
│  │  ├─ script.js
│  │  ├─ style.css
│  │  └ router/
│  │     └─ index.js
│  └─ main.js
├─ .env
├─ package.json
└─ vite.config.js
