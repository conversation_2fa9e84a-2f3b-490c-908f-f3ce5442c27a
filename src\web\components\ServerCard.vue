<template>
  <div 
    class="server-card"
    :class="{ active: isSelected }"
    @click="$emit('select', guild)"
  >
    <div class="server-icon">
      <img 
        v-if="guild.icon" 
        :src="iconUrl"
        :alt="guild.name"
        @error="onImageError"
      />
      <div v-else class="default-icon">{{ guild.name.charAt(0) }}</div>
    </div>
    <div class="server-info">
      <h3>{{ guild.name }}</h3>
      <div class="server-stats">
        <span class="member-count">
          <i class="icon">👥</i>
          {{ formatMemberCount(guild.approximate_member_count) }} 成員
        </span>
        <span v-if="guild.features?.length" class="features">
          <i class="icon">⭐</i>
          {{ guild.features.length }} 功能
        </span>
      </div>
      <div class="server-permissions">
        <span v-if="isOwner" class="permission-badge owner">擁有者</span>
        <span v-else-if="isAdmin" class="permission-badge admin">管理員</span>
        <span v-else class="permission-badge member">成員</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServerCard',
  props: {
    guild: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  },
  emits: ['select'],
  computed: {
    iconUrl() {
      return `https://cdn.discordapp.com/icons/${this.guild.id}/${this.guild.icon}.png?size=64`;
    },
    isOwner() {
      return this.guild.owner;
    },
    isAdmin() {
      return (this.guild.permissions & 0x8) === 0x8; // ADMINISTRATOR permission
    }
  },
  methods: {
    formatMemberCount(count) {
      if (!count) return '未知';
      if (count >= 1000) {
        return `${(count / 1000).toFixed(1)}k`;
      }
      return count.toString();
    },
    onImageError(event) {
      event.target.style.display = 'none';
      event.target.nextElementSibling.style.display = 'flex';
    }
  }
};
</script>

<style scoped>
.server-card {
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.server-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #7289da;
}

.server-card.active {
  border-color: #7289da;
  background-color: #f0f4ff;
  box-shadow: 0 8px 25px rgba(114, 137, 218, 0.2);
}

.server-icon {
  position: relative;
  flex-shrink: 0;
}

.server-icon img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
}

.default-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7289da, #5865f2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.5rem;
  text-transform: uppercase;
}

.server-info {
  flex: 1;
  min-width: 0;
}

.server-info h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.server-stats {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.75rem;
}

.member-count,
.features {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #666;
  font-size: 0.85rem;
}

.icon {
  font-size: 0.8rem;
}

.server-permissions {
  display: flex;
  gap: 0.5rem;
}

.permission-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.permission-badge.owner {
  background-color: #fef3c7;
  color: #d97706;
}

.permission-badge.admin {
  background-color: #dbeafe;
  color: #2563eb;
}

.permission-badge.member {
  background-color: #f3f4f6;
  color: #6b7280;
}

@media (max-width: 768px) {
  .server-card {
    padding: 1rem;
  }
  
  .server-icon img,
  .default-icon {
    width: 48px;
    height: 48px;
  }
  
  .default-icon {
    font-size: 1.2rem;
  }
}
</style>
