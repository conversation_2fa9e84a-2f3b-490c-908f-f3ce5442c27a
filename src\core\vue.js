import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createServer } from 'vite';
import path from 'path';
import connect from 'connect';
import serveStatic from 'serve-static';
import { handleApiRequest } from '../api/routes.js';
import fs from 'fs';

export const initVue = () => {
    const vue = createApp({});
    const pinia = createPinia();
    vue.use(pinia);
    return { vue, pinia };
}

export const initViteServer = async () => {
    console.log(`==================================`);
    console.log('Vue 初始化中...');

    const app = connect();
    const root = path.resolve(__dirname, '../../');

    const server = await createServer({
        configFile: path.resolve(root, 'vite.config.js'),
        root,
        base: '/',
        server: {
            port: 3000,
            host: 'localhost',
            middlewareMode: 'html'
        }
    });

    // API 路由處理 (必須在其他中間件之前)
    app.use('/api', handleApiRequest);

    // 靜態文件服務
    app.use(serveStatic(path.join(root, 'src/web')));

    // Vite 中間件
    app.use(server.middlewares);

    // SPA 路由處理 - 對於所有非 API 請求返回 index.html
    app.use((req, res, next) => {
        // 如果是 API 請求，跳過
        if (req.url.startsWith('/api/')) {
            return next();
        }

        // 如果是靜態資源請求，跳過
        if (req.url.includes('.')) {
            return next();
        }

        // 對於所有其他請求（包括 /callback），返回 index.html
        const indexHtml = path.join(root, 'src/web/index.html');
        res.setHeader('Content-Type', 'text/html');
        fs.createReadStream(indexHtml).pipe(res);
    });

    // 啟動服務器
    const httpServer = app.listen(3000, 'localhost', () => {
        console.log(`==================================`);
        console.log('Vite 伺服器啟動完成...');
        console.log(`http://localhost:3000`);
        console.log(`==================================`);
    });

    return { server, httpServer };
}

export default async () => {
    const { vue, pinia } = initVue();
    const { server, httpServer } = await initViteServer();
    return { vue, pinia, server, httpServer };
}