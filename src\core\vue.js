import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createServer } from 'vite';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const initVue = () => {
    const vue = createApp({});
    const pinia = createPinia();
    vue.use(pinia);
    return { vue, pinia };
}

export const initViteServer = async () => {
    console.log(`==================================`);
    console.log('Vue 開發伺服器啟動中...');

    const root = path.resolve(__dirname, '../../');

    try {
        const server = await createServer({
            configFile: path.resolve(root, 'vite.config.js'),
            root,
            base: '/'
        });

        // 啟動 Vite 開發服務器
        await server.listen();

        console.log(`==================================`);
        console.log('Vue 開發伺服器啟動完成...');
        console.log(`http://localhost:5173`);
        console.log(`==================================`);

        return { server };
    } catch (error) {
        console.error('Vue 開發伺服器啟動失敗:', error);
        throw error;
    }
}

export default async () => {
    const { vue, pinia } = initVue();
    const { server } = await initViteServer();
    return { vue, pinia, server };
}