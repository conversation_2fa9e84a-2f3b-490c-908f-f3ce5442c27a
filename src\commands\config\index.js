import { EmbedBuilder, SlashCommandBuilder, PermissionFlagsBits } from "discord.js";
import { printConfigSummary, validateConfig, getRoleId, getMessageId, getEmoji, getBotPrefix } from "../../utils/config-helper.js";

export const command = new SlashCommandBuilder()
    .setName("config")
    .setDescription("查看和管理 Bot 配置")
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
    .addSubcommand(subcommand =>
        subcommand
            .setName("show")
            .setDescription("顯示當前配置")
    )
    .addSubcommand(subcommand =>
        subcommand
            .setName("validate")
            .setDescription("驗證配置文件")
    )
    .addSubcommand(subcommand =>
        subcommand
            .setName("reload")
            .setDescription("重新載入配置文件")
    );

export const action = async (ctx) => {
    await ctx.deferReply({ ephemeral: true });

    const subcommand = ctx.options.getSubcommand();

    switch (subcommand) {
        case 'show':
            await showConfig(ctx);
            break;
        case 'validate':
            await validateConfigCommand(ctx);
            break;
        case 'reload':
            await reloadConfig(ctx);
            break;
        default:
            await ctx.editReply({
                content: '❌ 未知的子命令',
                ephemeral: true
            });
    }
};

async function showConfig(ctx) {
    const embed = new EmbedBuilder()
        .setTitle("🔧 Bot 配置資訊")
        .setColor("#0099ff")
        .setTimestamp();

    // 角色配置
    const roleId1 = getRoleId('ID_1');
    if (roleId1) {
        embed.addFields({
            name: "🎭 角色配置",
            value: `ID_1: \`${roleId1}\``,
            inline: false
        });
    }

    // 訊息配置
    const messageId1 = getMessageId('ID_1');
    if (messageId1) {
        embed.addFields({
            name: "💬 訊息配置",
            value: `ID_1: \`${messageId1}\``,
            inline: false
        });
    }

    // 表情符號配置
    const emoji1 = getEmoji('REACTION_1');
    if (emoji1) {
        embed.addFields({
            name: "😀 表情符號配置",
            value: `REACTION_1: ${emoji1}`,
            inline: false
        });
    }

    // Bot 設定
    const prefix = getBotPrefix();
    embed.addFields({
        name: "🤖 Bot 設定",
        value: `前綴: \`${prefix}\``,
        inline: false
    });

    await ctx.editReply({ embeds: [embed] });
}

async function validateConfigCommand(ctx) {
    const validation = validateConfig();
    
    const embed = new EmbedBuilder()
        .setTitle("✅ 配置驗證結果")
        .setColor(validation.isValid ? "#00ff00" : "#ff0000")
        .setTimestamp();

    if (validation.isValid) {
        embed.setDescription("✅ 配置文件完整且正確！");
    } else {
        embed.setDescription("❌ 配置文件有問題");
    }

    if (validation.errors.length > 0) {
        embed.addFields({
            name: "❌ 錯誤",
            value: validation.errors.map(error => `• ${error}`).join('\n'),
            inline: false
        });
    }

    if (validation.warnings.length > 0) {
        embed.addFields({
            name: "⚠️ 警告",
            value: validation.warnings.map(warning => `• ${warning}`).join('\n'),
            inline: false
        });
    }

    await ctx.editReply({ embeds: [embed] });
}

async function reloadConfig(ctx) {
    try {
        // 重新載入配置
        const { loadConfig } = await import("../../core/config.js");
        const config = loadConfig();
        global.config = config;

        // 在控制台打印配置摘要
        console.log('\n🔄 配置文件已重新載入');
        printConfigSummary();

        const embed = new EmbedBuilder()
            .setTitle("🔄 配置重新載入")
            .setDescription("✅ 配置文件已成功重新載入！")
            .setColor("#00ff00")
            .setTimestamp();

        await ctx.editReply({ embeds: [embed] });
    } catch (error) {
        console.error('重新載入配置時發生錯誤:', error);
        
        const embed = new EmbedBuilder()
            .setTitle("❌ 重新載入失敗")
            .setDescription(`重新載入配置時發生錯誤：\n\`\`\`${error.message}\`\`\``)
            .setColor("#ff0000")
            .setTimestamp();

        await ctx.editReply({ embeds: [embed] });
    }
}
