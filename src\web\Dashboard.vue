<template>
  <div class="dashboard">
    <header class="header">
      <h1>Discord Bot Dashboard</h1>
      <div class="user-info">
        <span v-if="userInfo">歡迎, {{ userInfo.username }}#{{ userInfo.discriminator }}</span>
        <button @click="logout" class="logout-btn">登出</button>
      </div>
    </header>

    <main class="main-content">
      <div class="loading" v-if="loading">
        <p>載入中...</p>
      </div>

      <div v-else class="content">
        <section class="servers-section">
          <h2>您的 Discord 伺服器</h2>
          <div v-if="guilds.length === 0" class="no-servers">
            <p>沒有找到伺服器，或您沒有管理權限。</p>
          </div>
          <div v-else class="servers-grid">
            <div 
              v-for="guild in guilds" 
              :key="guild.id" 
              class="server-card"
              :class="{ active: selectedGuild === guild.id }"
              @click="selectServer(guild)"
            >
              <div class="server-icon">
                <img 
                  v-if="guild.icon" 
                  :src="`https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png`"
                  :alt="guild.name"
                />
                <div v-else class="default-icon">{{ guild.name.charAt(0) }}</div>
              </div>
              <div class="server-info">
                <h3>{{ guild.name }}</h3>
                <p>{{ guild.member_count || '未知' }} 成員</p>
              </div>
            </div>
          </div>
        </section>

        <section v-if="selectedGuild" class="channels-section">
          <h2>{{ selectedGuildName }} 的頻道</h2>
          <div v-if="channels.length === 0" class="no-channels">
            <p>沒有找到頻道或載入中...</p>
          </div>
          <div v-else class="channels-list">
            <div 
              v-for="channel in channels" 
              :key="channel.id" 
              class="channel-item"
              :class="`channel-${channel.type}`"
            >
              <span class="channel-icon">
                <span v-if="channel.type === 0">#</span>
                <span v-else-if="channel.type === 2">🔊</span>
                <span v-else-if="channel.type === 4">📁</span>
                <span v-else>📝</span>
              </span>
              <span class="channel-name">{{ channel.name }}</span>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'Dashboard',
  data() {
    return {
      loading: true,
      userInfo: null,
      accessToken: localStorage.getItem('discord_access_token'),
      guilds: [],
      channels: [],
      selectedGuild: null,
      selectedGuildName: '',
    };
  },
  async created() {
    if (!this.accessToken) {
      this.$router.push('/');
      return;
    }
    
    await this.fetchUserInfo();
    await this.fetchGuilds();
    this.loading = false;
  },
  methods: {
    async fetchUserInfo() {
      try {
        const response = await axios.get('http://localhost:3000/api/user/info', {
          headers: { Authorization: `Bearer ${this.accessToken}` },
        });
        this.userInfo = response.data;
      } catch (error) {
        console.error('Error fetching user info:', error);
        if (error.response?.status === 401) {
          this.logout();
        }
      }
    },
    async fetchGuilds() {
      try {
        const response = await axios.get('http://localhost:3000/api/user/guilds', {
          headers: { Authorization: `Bearer ${this.accessToken}` },
        });
        this.guilds = response.data;
      } catch (error) {
        console.error('Error fetching guilds:', error);
        if (error.response?.status === 401) {
          this.logout();
        }
      }
    },
    async selectServer(guild) {
      this.selectedGuild = guild.id;
      this.selectedGuildName = guild.name;
      await this.fetchChannels();
    },
    async fetchChannels() {
      if (!this.selectedGuild) return;
      
      try {
        const response = await axios.get(`http://localhost:3000/api/guilds/${this.selectedGuild}/channels`, {
          headers: { Authorization: `Bearer ${this.accessToken}` },
        });
        this.channels = response.data;
      } catch (error) {
        console.error('Error fetching channels:', error);
      }
    },
    logout() {
      localStorage.removeItem('discord_access_token');
      this.$router.push('/');
    },
  },
};
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  background-color: #7289da;
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
  margin: 0;
  font-size: 1.5rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logout-btn {
  background-color: #f04747;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-btn:hover {
  background-color: #d73737;
}

.main-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.loading {
  text-align: center;
  padding: 2rem;
}

.servers-section {
  margin-bottom: 2rem;
}

.servers-section h2 {
  color: #333;
  margin-bottom: 1rem;
}

.no-servers {
  text-align: center;
  color: #666;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
}

.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.server-card {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.server-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.server-card.active {
  border-color: #7289da;
  background-color: #f0f4ff;
}

.server-icon img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.default-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #7289da;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
}

.server-info h3 {
  margin: 0 0 0.25rem 0;
  color: #333;
}

.server-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.channels-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
}

.channels-section h2 {
  margin-top: 0;
  color: #333;
}

.no-channels {
  text-align: center;
  color: #666;
  padding: 1rem;
}

.channels-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 0.5rem;
}

.channel-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.channel-item:hover {
  background-color: #f5f5f5;
}

.channel-icon {
  font-weight: bold;
  color: #7289da;
}

.channel-name {
  color: #333;
}
</style>
