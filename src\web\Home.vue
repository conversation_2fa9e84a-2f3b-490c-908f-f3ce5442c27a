<template>
  <div class="container">
    <h1>Discord Servers and Channels</h1>
    <button v-if="!accessToken" @click="login" class="login-btn">Login</button>
    <div v-else>
      <h2>Your Servers</h2>
      <select v-model="selectedGuild" @change="fetchChannels" class="select">
        <option value="" disabled>Select a server</option>
        <option v-for="guild in guilds" :key="guild.id" :value="guild.id">
          {{ guild.name }}
        </option>
      </select>
      <div v-if="channels.length" class="channels">
        <h3>Channels in {{ selectedGuildName }}</h3>
        <ul>
          <li v-for="channel in channels" :key="channel.id">{{ channel.name }}</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'Home',
  data() {
    return {
      accessToken: localStorage.getItem('discord_access_token'),
      guilds: [],
      channels: [],
      selectedGuild: '',
      selectedGuildName: '',
    };
  },
  methods: {
    login() {
      const clientId = import.meta.env.VITE_DISCORD_CLIENT_ID;
      const redirectUri = encodeURIComponent('http://localhost:5173/callback');
      const scope = encodeURIComponent('identify guilds');

      // 調試信息
      console.log('Client ID:', clientId);
      console.log('Redirect URI:', 'http://localhost:5173/callback');
      console.log('Encoded Redirect URI:', redirectUri);

      const authUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
      console.log('Auth URL:', authUrl);

      window.location.href = authUrl;
    },
    async fetchGuilds() {
      try {
        const response = await axios.get('http://localhost:3000/api/user/guilds', {
          headers: { Authorization: `Bearer ${this.accessToken}` },
        });
        this.guilds = response.data;
      } catch (error) {
        console.error('Error fetching guilds:', error);
        if (error.response?.status === 401) {
          localStorage.removeItem('discord_access_token');
          this.accessToken = null;
        }
      }
    },
    async fetchChannels() {
      if (!this.selectedGuild) return;
      
      const selectedGuildObj = this.guilds.find(g => g.id === this.selectedGuild);
      this.selectedGuildName = selectedGuildObj?.name || '';
      
      try {
        const response = await axios.get(`http://localhost:3000/api/guilds/${this.selectedGuild}/channels`, {
          headers: { Authorization: `Bearer ${this.accessToken}` },
        });
        this.channels = response.data;
      } catch (error) {
        console.error('Error fetching channels:', error);
      }
    },
  },
  watch: {
    accessToken(newToken) {
      if (newToken) {
        this.fetchGuilds();
      }
    },
  },
  created() {
    // 如果已經有 access token，重定向到 dashboard
    if (this.accessToken) {
      this.$router.push('/dashboard');
      return;
    }
  },
};
</script>

<style scoped>
.container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}
.login-btn {
  padding: 10px 20px;
  background-color: #7289da;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
.select {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
}
.channels {
  margin-top: 20px;
}
</style>
