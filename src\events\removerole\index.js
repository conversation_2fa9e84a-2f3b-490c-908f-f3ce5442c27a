// src/events/messageReactionRemove.js
import { Events } from 'discord.js';

export const event = {
  name: Events.MessageReactionRemove,
  once: false,
};

export const action = async (reaction, user) => {
  if (user.bot) return;

  if (reaction.partial) {
    try {
      await reaction.fetch();
    } catch (err) {
      console.error('❌ 無法 fetch reaction:', err);
      return;
    }
  }

  if (reaction.message.id !== process.env.MESSAGE_ID_1) return;
  if (reaction.emoji.name !== '✅') return;

  const guild = reaction.message.guild;
  const member = await guild.members.fetch(user.id);

  if (member.roles.cache.has(process.env.ROLE_ID_1)) {
    await member.roles.remove(process.env.ROLE_ID_1);
    console.log(`❌ 已為 ${user.tag} 移除角色`);
  }
};
