const colors = {
    success: '\x1b[32m',
    info: '\x1b[36m',
    error: '\x1b[31m',
    reset: '\x1b[0m'
};

export const logger = {
    success: (message, ...args) => {
        console.log(colors.success + message + colors.reset, ...args);
    },
    info: (message, ...args) => {
        console.log(colors.info + message + colors.reset, ...args);
    },
    error: (message, ...args) => {
        console.error(colors.error + message + colors.reset, ...args);
    }
};