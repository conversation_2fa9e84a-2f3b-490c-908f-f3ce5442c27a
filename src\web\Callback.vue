<template>
  <div>Loading...</div>
</template>

<script>
import axios from 'axios';

export default {
  async created() {
    const code = new URLSearchParams(window.location.search).get('code');
    if (code) {
      try {
        const response = await axios.get(`http://localhost:3000/api/auth/discord/callback?code=${code}`);
        localStorage.setItem('discord_access_token', response.data.access_token);
        this.$router.push('/');
      } catch (error) {
        console.error('Error handling callback:', error);
      }
    }
  },
};
</script>