import express from 'express';
import cors from 'cors';
import { handleApiRequest } from './routes.js';

const app = express();
const PORT = 3000;

// 啟用 CORS
app.use(cors({
    origin: ['http://localhost:5173', 'http://127.0.0.1:5173'],
    credentials: true
}));

// 解析 JSON
app.use(express.json());

// API 路由處理
app.use('/api', handleApiRequest);

// 健康檢查端點
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'API Server is running' });
});

// 啟動服務器
app.listen(PORT, 'localhost', () => {
    console.log(`==================================`);
    console.log('API 伺服器啟動完成...');
    console.log(`http://localhost:${PORT}`);
    console.log(`==================================`);
});

export default app;
