<template>
  <div class="home">
    <div class="hero-section">
      <div class="hero-content">
        <div class="logo">
          <div class="logo-icon">🤖</div>
          <h1>Discord Bot Dashboard</h1>
        </div>
        <p class="hero-description">
          管理您的 Discord 伺服器，查看頻道、成員和角色資訊
        </p>
        <div class="auth-section">
          <button v-if="!authStore.isLoggedIn" @click="login" class="login-btn">
            <span class="discord-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.**************.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
              </svg>
            </span>
            使用 Discord 登入
          </button>
          <div v-else class="logged-in-state">
            <div class="user-preview">
              <img
                v-if="authStore.userAvatar"
                :src="authStore.userAvatar"
                :alt="authStore.userDisplayName"
                class="user-avatar"
              />
              <div v-else class="default-avatar">{{ authStore.user?.username?.charAt(0) || '?' }}</div>
              <span class="user-name">歡迎回來，{{ authStore.userDisplayName }}</span>
            </div>
            <button @click="goToDashboard" class="dashboard-btn">
              前往控制台
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="features-section">
      <div class="features-container">
        <h2>功能特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🏠</div>
            <h3>伺服器管理</h3>
            <p>查看和管理您的 Discord 伺服器，獲取詳細的伺服器資訊</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">#</div>
            <h3>頻道瀏覽</h3>
            <p>瀏覽所有文字、語音和分類頻道，了解伺服器結構</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">👥</div>
            <h3>成員列表</h3>
            <p>查看伺服器成員資訊，包括暱稱和用戶資料</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎭</div>
            <h3>角色管理</h3>
            <p>檢視伺服器角色設定，了解權限分配情況</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthStore } from '../stores/auth.js';
import { config } from '../utils/config.js';

export default {
  name: 'Home',
  computed: {
    authStore() {
      return useAuthStore();
    }
  },
  async created() {
    // 如果用戶已經登入，直接跳轉到 Dashboard
    if (this.authStore.accessToken) {
      try {
        await this.authStore.checkAuth();
        if (this.authStore.isAuthenticated) {
          this.$router.push('/dashboard');
        }
      } catch (error) {
        console.log('檢查認證狀態失敗，保持在首頁');
      }
    }
  },
  methods: {
    login() {
      const clientId = config.DISCORD_CLIENT_ID;
      const redirectUri = encodeURIComponent(config.FRONTEND_URL + '/callback');
      const scope = encodeURIComponent('identify guilds');

      console.log('Client ID:', clientId);
      console.log('Redirect URI:', config.FRONTEND_URL + '/callback');

      if (!clientId || clientId === 'undefined') {
        alert('Discord Client ID 未設置，請檢查環境變數');
        return;
      }

      const authUrl = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
      console.log('Auth URL:', authUrl);

      window.location.href = authUrl;
    },
    goToDashboard() {
      this.$router.push('/dashboard');
    }
  }
};
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 英雄區域 */
.hero-section {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  text-align: center;
}

.hero-content {
  max-width: 600px;
  color: white;
}

.logo {
  margin-bottom: 2rem;
}

.logo-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounce 2s infinite;
}

.logo h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-description {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  line-height: 1.6;
}

.auth-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.login-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #7289da, #5865f2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  text-decoration: none;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #5865f2, #7289da);
}

.discord-icon {
  display: flex;
  align-items: center;
}

.logged-in-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-preview {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.default-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7289da, #5865f2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
  text-transform: uppercase;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-size: 1.1rem;
  font-weight: 500;
}

.dashboard-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dashboard-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* 功能特色區域 */
.features-section {
  background-color: #f8fafc;
  padding: 4rem 2rem;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.features-container h2 {
  margin: 0 0 3rem 0;
  font-size: 2rem;
  color: #1f2937;
  font-weight: 600;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #d1d5db;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.feature-card h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #1f2937;
  font-weight: 600;
}

.feature-card p {
  margin: 0;
  color: #6b7280;
  line-height: 1.6;
}

/* 動畫 */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .hero-content {
    padding: 1rem;
  }

  .logo h1 {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .login-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .features-section {
    padding: 3rem 1rem;
  }

  .features-container h2 {
    font-size: 1.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .logged-in-state {
    padding: 1.5rem;
  }

  .user-preview {
    flex-direction: column;
    text-align: center;
  }
}
</style>
