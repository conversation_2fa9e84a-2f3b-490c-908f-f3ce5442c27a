<template>
  <div class="callback-container">
    <div class="loading-spinner">
      <div class="spinner"></div>
    </div>
    <h2>正在處理登入...</h2>
    <p v-if="error" class="error">{{ error }}</p>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      error: null,
    };
  },
  async created() {
    const code = new URLSearchParams(window.location.search).get('code');
    const error = new URLSearchParams(window.location.search).get('error');

    console.log('Callback 頁面載入');
    console.log('URL:', window.location.href);
    console.log('Code:', code);
    console.log('Error:', error);

    if (error) {
      console.error('Discord 授權錯誤:', error);
      this.error = '登入被取消或發生錯誤: ' + error;
      setTimeout(() => {
        this.$router.push('/');
      }, 3000);
      return;
    }

    if (code) {
      try {
        console.log('發送 API 請求到:', `http://localhost:3000/api/auth/discord/callback?code=${code}`);
        const response = await axios.get(`http://localhost:3000/api/auth/discord/callback?code=${code}`);
        console.log('API 響應:', response.data);

        localStorage.setItem('discord_access_token', response.data.access_token);
        console.log('Token 已保存到 localStorage');

        // 重定向到 Dashboard 而不是首頁
        this.$router.push('/dashboard');
      } catch (error) {
        console.error('Error handling callback:', error);
        console.error('Error response:', error.response?.data);
        console.error('Error status:', error.response?.status);

        this.error = '登入處理失敗: ' + (error.response?.data?.error || error.message);
        setTimeout(() => {
          this.$router.push('/');
        }, 5000); // 增加到 5 秒以便查看錯誤
      }
    } else {
      this.error = '缺少授權碼';
      setTimeout(() => {
        this.$router.push('/');
      }, 3000);
    }
  },
};
</script>

<style scoped>
.callback-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  text-align: center;
}

.loading-spinner {
  margin-bottom: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #7289da;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

h2 {
  color: #333;
  margin: 0 0 1rem 0;
}

p {
  color: #666;
}

.error {
  color: #f04747;
  font-weight: bold;
}
</style>
