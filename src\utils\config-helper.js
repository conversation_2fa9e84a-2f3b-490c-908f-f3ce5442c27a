// 配置文件輔助工具
import { getConfig } from '../core/config.js';

/**
 * 獲取角色 ID
 * @param {string} roleKey - 角色鍵名 (例如: 'ID_1', 'ID_2')
 * @returns {string|null} 角色 ID
 */
export function getRoleId(roleKey) {
    const config = getConfig();
    return config.ROLE?.[roleKey] || null;
}

/**
 * 獲取訊息 ID
 * @param {string} messageKey - 訊息鍵名 (例如: 'ID_1', 'ID_2')
 * @returns {string|null} 訊息 ID
 */
export function getMessageId(messageKey) {
    const config = getConfig();
    return config.MESSAGE?.[messageKey] || null;
}

/**
 * 獲取表情符號
 * @param {string} emojiKey - 表情符號鍵名 (例如: 'REACTION_1', 'REACTION_2')
 * @returns {string|null} 表情符號
 */
export function getEmoji(emojiKey) {
    const config = getConfig();
    return config.EMOJI?.[emojiKey] || null;
}

/**
 * 獲取頻道 ID
 * @param {string} channelKey - 頻道鍵名
 * @returns {string|null} 頻道 ID
 */
export function getChannelId(channelKey) {
    const config = getConfig();
    return config.CHANNEL?.[channelKey] || null;
}

/**
 * 獲取 Bot 設定
 * @param {string} settingKey - 設定鍵名
 * @returns {any} 設定值
 */
export function getBotSetting(settingKey) {
    const config = getConfig();
    return config.BOT?.[settingKey] || null;
}

/**
 * 獲取 Bot 前綴
 * @returns {string} Bot 前綴
 */
export function getBotPrefix() {
    return getBotSetting('PREFIX') || '!';
}

/**
 * 檢查配置是否完整
 * @returns {object} 檢查結果
 */
export function validateConfig() {
    const config = getConfig();
    const errors = [];
    const warnings = [];

    // 檢查必要的配置
    if (!config.ROLE?.ID_1) {
        errors.push('缺少 ROLE.ID_1 配置');
    }

    if (!config.MESSAGE?.ID_1) {
        warnings.push('缺少 MESSAGE.ID_1 配置，某些功能可能無法正常運作');
    }

    if (!config.EMOJI?.REACTION_1) {
        warnings.push('缺少 EMOJI.REACTION_1 配置，將使用預設值 ✅');
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * 打印配置摘要
 */
export function printConfigSummary() {
    const config = getConfig();
    console.log('📋 配置文件摘要:');
    console.log('================');
    
    if (config.ROLE) {
        console.log('🎭 角色配置:');
        Object.entries(config.ROLE).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
    }

    if (config.MESSAGE) {
        console.log('💬 訊息配置:');
        Object.entries(config.MESSAGE).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
    }

    if (config.EMOJI) {
        console.log('😀 表情符號配置:');
        Object.entries(config.EMOJI).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
    }

    if (config.CHANNEL) {
        console.log('📺 頻道配置:');
        Object.entries(config.CHANNEL).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
    }

    if (config.BOT) {
        console.log('🤖 Bot 設定:');
        Object.entries(config.BOT).forEach(([key, value]) => {
            console.log(`   ${key}: ${value}`);
        });
    }

    console.log('================');

    // 驗證配置
    const validation = validateConfig();
    if (validation.errors.length > 0) {
        console.log('❌ 配置錯誤:');
        validation.errors.forEach(error => console.log(`   - ${error}`));
    }

    if (validation.warnings.length > 0) {
        console.log('⚠️  配置警告:');
        validation.warnings.forEach(warning => console.log(`   - ${warning}`));
    }

    if (validation.isValid && validation.warnings.length === 0) {
        console.log('✅ 配置文件完整且正確');
    }
}
