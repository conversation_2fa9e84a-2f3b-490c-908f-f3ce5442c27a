import { DiscordAuth } from './discord-auth.js';

const discordAuth = new DiscordAuth();

// API 路由處理器
export const handleApiRequest = async (req, res) => {
    const url = new URL(req.url, `http://${req.headers.host}`);
    const pathname = url.pathname;
    const searchParams = url.searchParams;

    // 設置 CORS 標頭
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    try {
        // Discord OAuth2 回調處理
        if (pathname === '/api/auth/discord/callback' && req.method === 'GET') {
            const code = searchParams.get('code');
            if (!code) {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: '缺少授權碼' }));
                return;
            }

            const tokenData = await discordAuth.handleCallback(code);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(tokenData));
            return;
        }

        // 獲取用戶伺服器列表
        if (pathname === '/api/user/guilds' && req.method === 'GET') {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.writeHead(401, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: '未提供有效的授權令牌' }));
                return;
            }

            const accessToken = authHeader.substring(7);
            const guilds = await discordAuth.getUserGuilds(accessToken);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(guilds));
            return;
        }

        // 獲取伺服器頻道列表
        if (pathname.startsWith('/api/guilds/') && pathname.endsWith('/channels') && req.method === 'GET') {
            const guildId = pathname.split('/')[3];
            const authHeader = req.headers.authorization;
            
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.writeHead(401, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: '未提供有效的授權令牌' }));
                return;
            }

            // 這裡需要使用 bot token 來獲取頻道資訊，因為用戶 token 可能沒有權限
            // 暫時返回空陣列，您可以根據需要實現
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify([]));
            return;
        }

        // 404 - 路由不存在
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: '路由不存在' }));

    } catch (error) {
        console.error('API 錯誤:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: error.message || '伺服器內部錯誤' }));
    }
};
