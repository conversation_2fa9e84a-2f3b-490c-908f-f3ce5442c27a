import { createServer } from 'vite';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function startVueServer() {
    const root = path.resolve(__dirname, '../');
    
    console.log('啟動 Vue 開發服務器...');
    console.log('Root 目錄:', root);
    
    try {
        const server = await createServer({
            configFile: path.resolve(root, 'vite.config.js'),
            root,
            base: '/'
        });

        await server.listen();
        
        console.log('Vue 開發服務器已啟動: http://localhost:5173');
    } catch (error) {
        console.error('啟動失敗:', error);
    }
}

startVueServer();
