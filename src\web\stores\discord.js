import { defineStore } from 'pinia';
import axios from 'axios';
import { config } from '../utils/config.js';
import { useAuthStore } from './auth.js';

export const useDiscordStore = defineStore('discord', {
  state: () => ({
    guilds: [],
    selectedGuild: null,
    guildInfo: null,
    channels: [],
    members: [],
    roles: [],
    loading: {
      guilds: false,
      guildInfo: false,
      channels: false,
      members: false,
      roles: false
    },
    error: null
  }),

  getters: {
    selectedGuildName: (state) => state.guildInfo?.name || '',
    textChannels: (state) => state.channels.filter(channel => channel.type === 0),
    voiceChannels: (state) => state.channels.filter(channel => channel.type === 2),
    categoryChannels: (state) => state.channels.filter(channel => channel.type === 4),
    onlineMembers: (state) => state.members.filter(member => member.status === 'online'),
    adminRoles: (state) => state.roles.filter(role => role.permissions & 0x8), // ADMINISTRATOR permission
  },

  actions: {
    async fetchGuilds() {
      const authStore = useAuthStore();
      if (!authStore.accessToken) return;

      this.loading.guilds = true;
      this.error = null;

      try {
        const response = await axios.get(`${config.API_BASE_URL}/api/user/guilds`, {
          headers: { Authorization: `Bearer ${authStore.accessToken}` }
        });
        this.guilds = response.data;
      } catch (error) {
        console.error('Error fetching guilds:', error);
        this.error = 'Failed to fetch guilds';
        if (error.response?.status === 401) {
          authStore.logout();
        }
      } finally {
        this.loading.guilds = false;
      }
    },

    async selectGuild(guildId) {
      this.selectedGuild = guildId;
      this.guildInfo = null;
      this.channels = [];
      this.members = [];
      this.roles = [];

      // Fetch all guild data
      await Promise.all([
        this.fetchGuildInfo(guildId),
        this.fetchChannels(guildId),
        this.fetchMembers(guildId),
        this.fetchRoles(guildId)
      ]);
    },

    async fetchGuildInfo(guildId) {
      const authStore = useAuthStore();
      if (!authStore.accessToken) return;

      this.loading.guildInfo = true;

      try {
        const response = await axios.get(`${config.API_BASE_URL}/api/guilds/${guildId}`, {
          headers: { Authorization: `Bearer ${authStore.accessToken}` }
        });
        this.guildInfo = response.data;
      } catch (error) {
        console.error('Error fetching guild info:', error);
        this.error = 'Failed to fetch guild information';
      } finally {
        this.loading.guildInfo = false;
      }
    },

    async fetchChannels(guildId) {
      const authStore = useAuthStore();
      if (!authStore.accessToken) return;

      this.loading.channels = true;

      try {
        const response = await axios.get(`${config.API_BASE_URL}/api/guilds/${guildId}/channels`, {
          headers: { Authorization: `Bearer ${authStore.accessToken}` }
        });
        this.channels = response.data.sort((a, b) => a.position - b.position);
      } catch (error) {
        console.error('Error fetching channels:', error);
        this.error = 'Failed to fetch channels';
      } finally {
        this.loading.channels = false;
      }
    },

    async fetchMembers(guildId) {
      const authStore = useAuthStore();
      if (!authStore.accessToken) return;

      this.loading.members = true;

      try {
        const response = await axios.get(`${config.API_BASE_URL}/api/guilds/${guildId}/members`, {
          headers: { Authorization: `Bearer ${authStore.accessToken}` }
        });
        this.members = response.data;
      } catch (error) {
        console.error('Error fetching members:', error);
        this.error = 'Failed to fetch members';
      } finally {
        this.loading.members = false;
      }
    },

    async fetchRoles(guildId) {
      const authStore = useAuthStore();
      if (!authStore.accessToken) return;

      this.loading.roles = true;

      try {
        const response = await axios.get(`${config.API_BASE_URL}/api/guilds/${guildId}/roles`, {
          headers: { Authorization: `Bearer ${authStore.accessToken}` }
        });
        this.roles = response.data.sort((a, b) => b.position - a.position);
      } catch (error) {
        console.error('Error fetching roles:', error);
        this.error = 'Failed to fetch roles';
      } finally {
        this.loading.roles = false;
      }
    },

    clearGuildData() {
      this.selectedGuild = null;
      this.guildInfo = null;
      this.channels = [];
      this.members = [];
      this.roles = [];
    }
  }
});
