{"name": "discord-bot", "version": "1.0.0", "type": "module", "main": "src/main.js", "scripts": {"dev:bot": "nodemon --watch src --ext js,json --exec \"powershell -Command Clear-Host; vite-node src/main.js\"", "dev:web": "vite", "dev": "nodemon --watch src --ext js,json --exec \"powershell -Command Clear-Host; vite-node src/main.js\"", "start": "vite-node src/main.js"}, "dependencies": {"@vitejs/plugin-vue": "^5.2.4", "axios": "^1.9.0", "concurrently": "^9.1.2", "connect": "^3.7.0", "cors": "^2.8.5", "discord.js": "^14.19.3", "dotenv": "^16.5.0", "express": "^5.1.0", "fast-glob": "^3.3.3", "js-yaml": "^4.1.0", "nodemon": "^3.1.10", "pinia": "^3.0.2", "serve-static": "^2.2.0", "vite": "^5.1.4", "vite-node": "^3.1.4", "vite-router": "^1.0.1", "vue": "^3.5.14", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^20.11.19"}}