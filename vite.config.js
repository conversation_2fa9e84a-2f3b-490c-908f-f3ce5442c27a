import { defineConfig } from 'vite';
import path from 'path';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig({  
    plugins: [vue()],
    root: path.resolve(__dirname, 'src/web'),
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src'),
            '@core': path.resolve(__dirname, 'src/core'),
            '@store': path.resolve(__dirname, 'src/store'),
            '@commands': path.resolve(__dirname, 'src/commands'),
            '@web': path.resolve(__dirname, 'src/web'),
            '@utils': path.resolve(__dirname, 'src/utils'),
            '@events': path.resolve(__dirname, 'src/events')
        }
    },
    server: {
        port: 3001,
        open: true,
        watch: {
            usePolling: true
        },
        strictPort: true,  // 添加這行
        force: true        // 添加這行
    },
    optimizeDeps: {
        include: ['vue', 'pinia']
    }
})