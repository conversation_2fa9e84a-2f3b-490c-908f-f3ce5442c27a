import { defineConfig } from 'vite';
import path from 'path';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig({
    plugins: [vue()],
    root: path.resolve(__dirname, 'src/web'),
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src'),
            '@core': path.resolve(__dirname, 'src/core'),
            '@store': path.resolve(__dirname, 'src/store'),
            '@commands': path.resolve(__dirname, 'src/commands'),
            '@web': path.resolve(__dirname, 'src/web'),
            '@utils': path.resolve(__dirname, 'src/utils'),
            '@events': path.resolve(__dirname, 'src/events')
        }
    },
    // 移除 server 配置，讓它通過 vue.js 中的中間件模式運行
    optimizeDeps: {
        include: ['vue', 'pinia']
    },
    // 確保 SPA 模式正常工作
    appType: 'spa'
})