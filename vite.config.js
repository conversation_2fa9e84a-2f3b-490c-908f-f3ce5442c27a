import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig(({ mode }) => {
    // 加載環境變數
    const env = loadEnv(mode, __dirname, '');

    return {
        plugins: [vue()],
        root: path.resolve(__dirname, 'src/web'),
        envDir: __dirname, // 指定 .env 文件的目錄
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src'),
                '@core': path.resolve(__dirname, 'src/core'),
                '@store': path.resolve(__dirname, 'src/store'),
                '@commands': path.resolve(__dirname, 'src/commands'),
                '@web': path.resolve(__dirname, 'src/web'),
                '@utils': path.resolve(__dirname, 'src/utils'),
                '@events': path.resolve(__dirname, 'src/events')
            }
        },
        server: {
            port: 5173, // Vue 預設端口
            host: 'localhost',
            cors: true
        },
        optimizeDeps: {
            include: ['vue', 'pinia']
        },
        define: {
            // 手動定義環境變數以確保它們可用
            __VITE_DISCORD_CLIENT_ID__: JSON.stringify(env.VITE_DISCORD_CLIENT_ID)
        }
    }
})