import { Client, GatewayIntentBits } from 'discord.js';
import dotenv from 'dotenv';
import { useAppStore } from '@store/app';
import vueInit from '@core/vue';
import { loadCommands, loadEvents } from '@core/loader';
import { loadConfig } from '@core/config';
import { printConfigSummary, validateConfig } from './utils/config-helper.js';
import './api/server.js'; // 啟動 API 服務器

const init = async () => {
    dotenv.config();

    // 載入並驗證配置文件
    console.log('🔧 載入配置文件...');
    const config = loadConfig('@src/config.yml');
    global.config = config;

    // 打印配置摘要
    printConfigSummary();

    // 驗證配置
    const validation = validateConfig();
    if (!validation.isValid) {
        console.error('❌ 配置文件有錯誤，Bot 可能無法正常運作');
        validation.errors.forEach(error => console.error(`   - ${error}`));
    }

    vueInit();
    loadCommands();

    const bot = new Client({
        intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.MessageContent,
        ],
        partials: ['MESSAGE', 'CHANNEL', 'REACTION'],
    });

    const appStore = useAppStore();
    appStore.bot = bot;
    loadEvents();
    await bot.login(process.env.TOKEN);
  

};

init().catch(console.error);
