import { Client, Events, GatewayIntentBits } from 'discord.js';
import dotenv from 'dotenv';
import { useAppStore } from '@store/app';

import vueInit from '@core/vue';
import { loadCommands, loadEvents } from '@core/loader';

const init = async () => {
    dotenv.config();
    vueInit();
    loadCommands();
    
    const bot = new Client({
        intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.MessageContent,
        ],
        partials: ['MESSAGE', 'CHANNEL', 'REACTION'],
    });

    const appStore = useAppStore();
    appStore.bot = bot;
    loadEvents();
    await bot.login(process.env.TOKEN);
  

};

init().catch(console.error);
