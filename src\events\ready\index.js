import { Events } from "discord.js";

export const event = {
    name: Events.ClientReady,
    once: true,
};

export const action = async (c) => {


    console.log(`==================================`);
    console.log(`Ready! Logged in as ${c.user.tag}`);
    console.log(`==================================`);
    console.log(`App ID: ${process.env.ApplicationID}`);
    console.log(`Channel ID: ${process.env.CHANNEL_ID_1}`);
    console.log(`Guild ID: ${process.env.GUILD_TEST}`);
    console.log(`Role ID: ${process.env.ROLE_ID_1}`);
    console.log(`Message ID: ${process.env.MESSAGE_ID_1}`);
    console.log(`Emoji ID: ${process.env.EMOJI_ID_1}`);
    console.log(`==================================`);
    // console.log(`fetch 舊訊息`)
    // try {
    //     const channel = await c.channels.fetch(process.env.CHANNEL_ID_1);
        
    //     if (!channel.isTextBased()) {
    //         console.error('❌ 頻道不是文字頻道，無法讀取訊息');
    //         return;
    //     }

    //     const message = await channel.messages.fetch(process.env.MESSAGE_ID_1);

    //     console.log(`✅ 成功預載目標訊息: ${message.id}`);
    //     console.log(`訊息內容: "${message.content}"`);
    //     console.log(`可監聽反應: ${message.reactions.cache.size} 個`);
    // } catch (err) {
    //     console.error('❌ 預載訊息失敗:', err);
    //     console.error('請檢查:');
    //     console.error('1. CHANNEL_ID_1 和 MESSAGE_ID_1 是否正確');
    //     console.error('2. 機器人是否有 "View Channel" 和 "Read Message History" 權限');
    // }

};