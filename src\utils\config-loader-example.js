// 示例：如何使用 @src/config.yml 路徑別名

import { loadConfig } from '@core/config.js';
import { getRoleId, getMessageId } from '@utils/config-helper.js';

/**
 * 示例 1: 直接使用路徑別名載入配置
 */
export function loadConfigWithAlias() {
    console.log('📋 使用路徑別名載入配置...');
    
    // 使用 @src/ 別名
    const config = loadConfig('@src/config.yml');
    
    console.log('配置載入完成:', config);
    return config;
}

/**
 * 示例 2: 在函數中使用配置
 */
export function exampleUsingConfig() {
    // 使用輔助函數獲取配置值
    const roleId = getRoleId('ID_1');
    const messageId = getMessageId('ID_1');
    
    console.log('角色 ID:', roleId);
    console.log('訊息 ID:', messageId);
    
    return { roleId, messageId };
}

/**
 * 示例 3: 動態載入不同的配置文件
 */
export function loadDifferentConfigs() {
    // 載入主配置
    const mainConfig = loadConfig('@src/config.yml');
    
    // 如果有其他配置文件，也可以這樣載入
    // const devConfig = loadConfig('@src/config.dev.yml');
    // const prodConfig = loadConfig('@src/config.prod.yml');
    
    return {
        main: mainConfig,
        // dev: devConfig,
        // prod: prodConfig
    };
}

/**
 * 示例 4: 配置驗證和錯誤處理
 */
export function validateAndLoadConfig() {
    try {
        const config = loadConfig('@src/config.yml');
        
        // 驗證必要的配置項
        if (!config.ROLE?.ID_1) {
            throw new Error('缺少必要的角色配置 ROLE.ID_1');
        }
        
        if (!config.EMOJI?.REACTION_1) {
            console.warn('⚠️ 缺少表情符號配置，將使用預設值');
            config.EMOJI = config.EMOJI || {};
            config.EMOJI.REACTION_1 = '✅';
        }
        
        console.log('✅ 配置驗證通過');
        return config;
        
    } catch (error) {
        console.error('❌ 配置載入或驗證失敗:', error.message);
        return null;
    }
}

/**
 * 示例 5: 配置熱重載
 */
export function setupConfigHotReload() {
    // 這個功能需要配合 nodemon 的文件監控
    console.log('🔄 配置熱重載已設置');
    console.log('修改 config.yml 文件時，Bot 將自動重啟');
    
    // 在 package.json 中已經設置了 --ext js,json,yml
    // 這意味著當 .yml 文件改變時，nodemon 會重啟應用
}

// 導出所有示例函數
export default {
    loadConfigWithAlias,
    exampleUsingConfig,
    loadDifferentConfigs,
    validateAndLoadConfig,
    setupConfigHotReload
};
