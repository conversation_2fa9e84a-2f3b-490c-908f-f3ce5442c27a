import { defineConfig } from 'vite';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig({
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src'),
            '@core': path.resolve(__dirname, 'src/core'),
            '@store': path.resolve(__dirname, 'src/store'),
            '@commands': path.resolve(__dirname, 'src/commands'),
            '@utils': path.resolve(__dirname, 'src/utils'),
            '@events': path.resolve(__dirname, 'src/events'),
            '@config': path.resolve(__dirname, 'src/config.yml'),
            '@src': path.resolve(__dirname, 'src')
        }
    },
    // 針對 Node.js 環境的配置
    ssr: {
        noExternal: ['js-yaml']
    }
});
