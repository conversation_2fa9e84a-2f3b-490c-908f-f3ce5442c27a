import yml from 'js-yaml';
import path from 'path';
import fs from 'fs';

let config = null;

export function loadConfig(filePath = './src/config.yml' ) {
    try {
        const absolutePath = path.resolve(filePath);
        const fileContent = fs.readFileSync(absolutePath, 'utf8');
        config = yml.load(fileContent);
        return config;
    } catch (e) {
        console.error('❌ 讀取 config.yml 發生錯誤:', e.message);
        return {};
    }
}

export function getConfig() {
    return config || loadConfig();
}