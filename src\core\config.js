import yml from 'js-yaml';
import path from 'path';
import fs from 'fs';

let config = null;

export function loadConfig(filePath = './src/config.yml' ) {
    try {
        // 支援路徑別名和相對路徑
        let resolvedPath;
        if (filePath.startsWith('@src/')) {
            // 處理 @src/ 別名
            resolvedPath = path.resolve(process.cwd(), 'src', filePath.replace('@src/', ''));
        } else {
            resolvedPath = path.resolve(filePath);
        }

        const fileContent = fs.readFileSync(resolvedPath, 'utf8');
        config = yml.load(fileContent);
        console.log(`✅ 配置文件已載入: ${resolvedPath}`);
        return config;
    } catch (e) {
        console.error('❌ 讀取 config.yml 發生錯誤:', e.message);
        return {};
    }
}

export function getConfig() {
    return config || loadConfig();
}