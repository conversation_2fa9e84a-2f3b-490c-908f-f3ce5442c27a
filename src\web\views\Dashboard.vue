<template>
  <div class="dashboard">
    <!-- 頂部導航欄 -->
    <header class="header">
      <div class="header-left">
        <h1>Discord Bot Dashboard</h1>
        <div class="breadcrumb" v-if="discordStore.selectedGuild">
          <span class="breadcrumb-item">伺服器</span>
          <span class="breadcrumb-separator">></span>
          <span class="breadcrumb-item current">{{ discordStore.selectedGuildName }}</span>
        </div>
      </div>
      <div class="header-right">
        <div class="user-info" v-if="authStore.user">
          <img
            v-if="authStore.userAvatar"
            :src="authStore.userAvatar"
            :alt="authStore.userDisplayName"
            class="user-avatar"
          />
          <span class="user-name">{{ authStore.userDisplayName }}</span>
          <button @click="logout" class="logout-btn">登出</button>
        </div>
      </div>
    </header>

    <!-- 主要內容區域 -->
    <main class="main-content">
      <LoadingSpinner v-if="authStore.loading" message="載入用戶資訊中..." />

      <div v-else class="content">
        <!-- 伺服器選擇區域 -->
        <section class="servers-section">
          <div class="section-header">
            <h2>您的 Discord 伺服器</h2>
            <button @click="refreshGuilds" class="refresh-btn" :disabled="discordStore.loading.guilds">
              <span class="icon">🔄</span>
              重新整理
            </button>
          </div>

          <LoadingSpinner v-if="discordStore.loading.guilds" message="載入伺服器列表中..." />

          <div v-else-if="discordStore.guilds.length === 0" class="no-servers">
            <div class="empty-state">
              <i class="icon">🏠</i>
              <h3>沒有找到伺服器</h3>
              <p>您可能沒有管理權限，或者 Bot 還沒有加入任何伺服器。</p>
            </div>
          </div>

          <div v-else class="servers-grid">
            <ServerCard
              v-for="guild in discordStore.guilds"
              :key="guild.id"
              :guild="guild"
              :is-selected="discordStore.selectedGuild === guild.id"
              @select="selectServer"
            />
          </div>
        </section>

        <!-- 伺服器詳細資訊區域 -->
        <section v-if="discordStore.selectedGuild" class="guild-details">
          <div class="guild-overview">
            <div class="guild-header">
              <div class="guild-icon">
                <img
                  v-if="discordStore.guildInfo?.icon"
                  :src="getGuildIconUrl(discordStore.guildInfo)"
                  :alt="discordStore.guildInfo.name"
                />
                <div v-else class="default-guild-icon">
                  {{ discordStore.guildInfo?.name?.charAt(0) || '?' }}
                </div>
              </div>
              <div class="guild-info">
                <h2>{{ discordStore.guildInfo?.name || '載入中...' }}</h2>
                <div class="guild-stats">
                  <div class="stat">
                    <span class="stat-label">成員數量</span>
                    <span class="stat-value">{{ discordStore.guildInfo?.approximate_member_count || '未知' }}</span>
                  </div>
                  <div class="stat">
                    <span class="stat-label">在線成員</span>
                    <span class="stat-value">{{ discordStore.guildInfo?.approximate_presence_count || '未知' }}</span>
                  </div>
                  <div class="stat">
                    <span class="stat-label">頻道數量</span>
                    <span class="stat-value">{{ discordStore.channels.length }}</span>
                  </div>
                  <div class="stat">
                    <span class="stat-label">角色數量</span>
                    <span class="stat-value">{{ discordStore.roles.length }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 標籤頁導航 -->
          <div class="tabs">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              class="tab-button"
              :class="{ active: activeTab === tab.id }"
              @click="activeTab = tab.id"
            >
              <span class="tab-icon">{{ tab.icon }}</span>
              {{ tab.label }}
              <span v-if="tab.count !== undefined" class="tab-count">({{ tab.count }})</span>
            </button>
          </div>

          <!-- 標籤頁內容 -->
          <div class="tab-content">
            <!-- 頻道列表 -->
            <div v-if="activeTab === 'channels'" class="tab-panel">
              <LoadingSpinner v-if="discordStore.loading.channels" message="載入頻道列表中..." />
              <ChannelList v-else :channels="discordStore.channels" />
            </div>

            <!-- 成員列表 -->
            <div v-if="activeTab === 'members'" class="tab-panel">
              <LoadingSpinner v-if="discordStore.loading.members" message="載入成員列表中..." />
              <div v-else class="members-list">
                <div v-if="discordStore.members.length === 0" class="no-data">
                  <i class="icon">👥</i>
                  <p>沒有找到成員資料</p>
                </div>
                <div v-else class="members-grid">
                  <div
                    v-for="member in discordStore.members"
                    :key="member.user.id"
                    class="member-card"
                  >
                    <img
                      v-if="member.user.avatar"
                      :src="getUserAvatarUrl(member.user)"
                      :alt="member.user.username"
                      class="member-avatar"
                    />
                    <div v-else class="default-avatar">{{ member.user.username.charAt(0) }}</div>
                    <div class="member-info">
                      <div class="member-name">{{ member.nick || member.user.username }}</div>
                      <div class="member-username">{{ member.user.username }}#{{ member.user.discriminator }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 角色列表 -->
            <div v-if="activeTab === 'roles'" class="tab-panel">
              <LoadingSpinner v-if="discordStore.loading.roles" message="載入角色列表中..." />
              <div v-else class="roles-list">
                <div v-if="discordStore.roles.length === 0" class="no-data">
                  <i class="icon">🎭</i>
                  <p>沒有找到角色資料</p>
                </div>
                <div v-else class="roles-grid">
                  <div
                    v-for="role in discordStore.roles"
                    :key="role.id"
                    class="role-card"
                    :style="{ borderLeftColor: role.color ? `#${role.color.toString(16).padStart(6, '0')}` : '#99aab5' }"
                  >
                    <div class="role-info">
                      <div class="role-name" :style="{ color: role.color ? `#${role.color.toString(16).padStart(6, '0')}` : '#99aab5' }">
                        {{ role.name }}
                      </div>
                      <div class="role-details">
                        <span class="role-members">{{ role.member_count || 0 }} 成員</span>
                        <span v-if="role.permissions & 0x8" class="role-admin">管理員</span>
                        <span v-if="role.mentionable" class="role-mentionable">可提及</span>
                        <span v-if="role.hoist" class="role-hoisted">分開顯示</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script>
import { useAuthStore } from '../stores/auth.js';
import { useDiscordStore } from '../stores/discord.js';
import LoadingSpinner from '../components/LoadingSpinner.vue';
import ServerCard from '../components/ServerCard.vue';
import ChannelList from '../components/ChannelList.vue';

export default {
  name: 'Dashboard',
  components: {
    LoadingSpinner,
    ServerCard,
    ChannelList
  },
  data() {
    return {
      activeTab: 'channels'
    };
  },
  computed: {
    authStore() {
      return useAuthStore();
    },
    discordStore() {
      return useDiscordStore();
    },
    tabs() {
      return [
        {
          id: 'channels',
          label: '頻道',
          icon: '#',
          count: this.discordStore.channels.length
        },
        {
          id: 'members',
          label: '成員',
          icon: '👥',
          count: this.discordStore.members.length
        },
        {
          id: 'roles',
          label: '角色',
          icon: '🎭',
          count: this.discordStore.roles.length
        }
      ];
    }
  },
  async created() {
    // 檢查認證狀態
    if (!this.authStore.accessToken) {
      this.$router.push('/');
      return;
    }

    // 確保用戶已認證
    await this.authStore.checkAuth();

    if (!this.authStore.isAuthenticated) {
      this.$router.push('/');
      return;
    }

    // 載入伺服器列表
    await this.discordStore.fetchGuilds();
  },
  methods: {
    async selectServer(guild) {
      await this.discordStore.selectGuild(guild.id);
      this.activeTab = 'channels'; // 重置到頻道標籤
    },
    async refreshGuilds() {
      await this.discordStore.fetchGuilds();
    },
    logout() {
      this.authStore.logout();
      this.discordStore.clearGuildData();
      this.$router.push('/');
    },
    getGuildIconUrl(guild) {
      if (!guild?.icon) return null;
      return `https://cdn.discordapp.com/icons/${guild.id}/${guild.icon}.png?size=128`;
    },
    getUserAvatarUrl(user) {
      if (!user?.avatar) return null;
      return `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png?size=64`;
    }
  }
};
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
  background-color: #f8fafc;
}

/* 頂部導航欄 */
.header {
  background: linear-gradient(135deg, #7289da, #5865f2);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left h1 {
  margin: 0 0 0.25rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  opacity: 0.9;
}

.breadcrumb-item.current {
  font-weight: 500;
}

.breadcrumb-separator {
  opacity: 0.7;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-name {
  font-weight: 500;
}

.logout-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 主要內容區域 */
.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 伺服器選擇區域 */
.servers-section {
  background-color: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #e2e8f0;
  border-color: #cbd5e1;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.no-servers {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state {
  max-width: 400px;
  margin: 0 auto;
}

.empty-state .icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-size: 1.1rem;
}

.empty-state p {
  margin: 0;
  color: #6b7280;
  line-height: 1.5;
}

.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

/* 伺服器詳細資訊區域 */
.guild-details {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guild-overview {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1px solid #e2e8f0;
}

.guild-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.guild-icon img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.default-guild-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7289da, #5865f2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 2rem;
  text-transform: uppercase;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.guild-info h2 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.guild-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

/* 標籤頁 */
.tabs {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  color: #374151;
  background-color: rgba(114, 137, 218, 0.1);
}

.tab-button.active {
  color: #7289da;
  border-bottom-color: #7289da;
  background-color: white;
}

.tab-icon {
  font-size: 1rem;
}

.tab-count {
  background-color: #e2e8f0;
  color: #6b7280;
  padding: 0.125rem 0.375rem;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
}

.tab-button.active .tab-count {
  background-color: #dbeafe;
  color: #2563eb;
}

/* 標籤頁內容 */
.tab-content {
  min-height: 400px;
}

.tab-panel {
  padding: 2rem;
}

/* 成員列表 */
.members-list,
.roles-list {
  width: 100%;
}

.no-data {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.no-data .icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  opacity: 0.5;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.member-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.member-card:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
}

.default-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7289da, #5865f2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  text-transform: uppercase;
  flex-shrink: 0;
}

.member-info {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-username {
  font-size: 0.8rem;
  color: #6b7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 角色列表 */
.roles-grid {
  display: grid;
  gap: 0.75rem;
}

.role-card {
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #99aab5;
  transition: all 0.2s ease;
}

.role-card:hover {
  background-color: #f1f5f9;
  transform: translateX(4px);
}

.role-name {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.role-details {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

.role-members {
  color: #6b7280;
  font-size: 0.85rem;
}

.role-admin,
.role-mentionable,
.role-hoisted {
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-admin {
  background-color: #fef3c7;
  color: #d97706;
}

.role-mentionable {
  background-color: #dbeafe;
  color: #2563eb;
}

.role-hoisted {
  background-color: #f3e8ff;
  color: #7c3aed;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-left,
  .header-right {
    text-align: center;
  }

  .main-content {
    padding: 1rem;
  }

  .servers-grid {
    grid-template-columns: 1fr;
  }

  .guild-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .guild-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .tabs {
    overflow-x: auto;
  }

  .tab-button {
    white-space: nowrap;
    padding: 0.75rem 1rem;
  }

  .members-grid {
    grid-template-columns: 1fr;
  }
}
</style>
