import { defineStore } from 'pinia';
import axios from 'axios';
import { config } from '../utils/config.js';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    accessToken: localStorage.getItem('discord_access_token'),
    isAuthenticated: false,
    loading: false,
    error: null
  }),

  getters: {
    isLoggedIn: (state) => !!state.accessToken && state.isAuthenticated,
    userDisplayName: (state) => {
      if (!state.user) return '';
      return state.user.discriminator === '0' 
        ? state.user.username 
        : `${state.user.username}#${state.user.discriminator}`;
    },
    userAvatar: (state) => {
      if (!state.user || !state.user.avatar) return null;
      return `https://cdn.discordapp.com/avatars/${state.user.id}/${state.user.avatar}.png`;
    }
  },

  actions: {
    async login(accessToken) {
      this.loading = true;
      this.error = null;
      
      try {
        localStorage.setItem('discord_access_token', accessToken);
        this.accessToken = accessToken;
        
        await this.fetchUserInfo();
        this.isAuthenticated = true;
      } catch (error) {
        this.error = error.message;
        this.logout();
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchUserInfo() {
      if (!this.accessToken) {
        throw new Error('No access token available');
      }

      try {
        const response = await axios.get(`${config.API_BASE_URL}/api/user/info`, {
          headers: { Authorization: `Bearer ${this.accessToken}` }
        });
        this.user = response.data;
      } catch (error) {
        console.error('Error fetching user info:', error);
        if (error.response?.status === 401) {
          this.logout();
        }
        throw new Error('Failed to fetch user information');
      }
    },

    logout() {
      this.user = null;
      this.accessToken = null;
      this.isAuthenticated = false;
      this.error = null;
      localStorage.removeItem('discord_access_token');
    },

    async checkAuth() {
      if (this.accessToken && !this.isAuthenticated) {
        try {
          await this.fetchUserInfo();
          this.isAuthenticated = true;
        } catch (error) {
          this.logout();
        }
      }
    }
  }
});
