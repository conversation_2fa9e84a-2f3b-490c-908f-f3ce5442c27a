<template>
  <div id="app">
    <router-view v-if="isCallbackRoute" />
    <div v-else class="container">
      <h1>Discord Servers and Channels</h1>
      <button v-if="!accessToken" @click="login" class="login-btn">Login</button>
      <div v-else>
        <h2>Your Servers</h2>
        <select v-model="selectedGuild" @change="fetchChannels" class="select">
          <option value="" disabled>Select a server</option>
          <option v-for="guild in guilds" :key="guild.id" :value="guild.id">
            {{ guild.name }}
          </option>
        </select>
        <div v-if="channels.length" class="channels">
          <h3>Channels in {{ selectedGuildName }}</h3>
          <ul>
            <li v-for="channel in channels" :key="channel.id">{{ channel.name }}</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'App',
  data() {
    return {
      accessToken: localStorage.getItem('discord_access_token') || null,
      guilds: [],
      channels: [],
      selectedGuild: '',
      selectedGuildName: '',
    };
  },
  computed: {
    isCallbackRoute() {
      return this.$route && this.$route.path === '/callback';
    },
  },
  methods: {
    login() {
      const clientId = import.meta.env.VITE_DISCORD_CLIENT_ID;
      const redirectUri = encodeURIComponent('http://localhost:3000/callback');
      const scope = encodeURIComponent('identify guilds');
      window.location.href = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;
    },
    async fetchGuilds() {
      try {
        const response = await axios.get('http://localhost:3000/api/user/guilds', {
          headers: { Authorization: `Bearer ${this.accessToken}` },
        });
        this.guilds = response.data;
      } catch (error) {
        console.error('Error fetching guilds:', error);
      }
    },
    async fetchChannels() {
      if (!this.selectedGuild) return;
      try {
        const response = await axios.get(`http://localhost:3000/api/guilds/${this.selectedGuild}/channels`, {
          headers: { Authorization: `Bearer ${this.accessToken}` },
        });
        this.channels = response.data;
        this.selectedGuildName = this.guilds.find(g => g.id === this.selectedGuild)?.name || '';
      } catch (error) {
        console.error('Error fetching channels:', error);
      }
    },
  },
  watch: {
    accessToken(newToken) {
      if (newToken) {
        this.fetchGuilds();
      }
    },
  },
  created() {
    if (this.accessToken) {
      this.fetchGuilds();
    }
  },
};
</script>

<style scoped>
.container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}
.login-btn {
  padding: 10px 20px;
  background-color: #7289da;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
.select {
  width: 100%;
  padding: 10px;
  margin: 10px 0;
}
.channels {
  margin-top: 20px;
}
</style>