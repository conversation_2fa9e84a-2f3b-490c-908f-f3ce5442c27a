<template>
  <div class="channel-list">
    <div class="channel-section" v-if="textChannels.length > 0">
      <h4 class="section-title">
        <i class="icon">#</i>
        文字頻道
        <span class="count">({{ textChannels.length }})</span>
      </h4>
      <div class="channels">
        <div 
          v-for="channel in textChannels" 
          :key="channel.id" 
          class="channel-item text-channel"
          :class="{ nsfw: channel.nsfw }"
        >
          <span class="channel-icon">#</span>
          <span class="channel-name">{{ channel.name }}</span>
          <span v-if="channel.nsfw" class="nsfw-badge">NSFW</span>
          <span v-if="channel.topic" class="channel-topic">{{ channel.topic }}</span>
        </div>
      </div>
    </div>

    <div class="channel-section" v-if="voiceChannels.length > 0">
      <h4 class="section-title">
        <i class="icon">🔊</i>
        語音頻道
        <span class="count">({{ voiceChannels.length }})</span>
      </h4>
      <div class="channels">
        <div 
          v-for="channel in voiceChannels" 
          :key="channel.id" 
          class="channel-item voice-channel"
        >
          <span class="channel-icon">🔊</span>
          <span class="channel-name">{{ channel.name }}</span>
          <span v-if="channel.user_limit" class="user-limit">
            限制: {{ channel.user_limit }} 人
          </span>
          <span v-if="channel.bitrate" class="bitrate">
            {{ Math.round(channel.bitrate / 1000) }}kbps
          </span>
        </div>
      </div>
    </div>

    <div class="channel-section" v-if="categoryChannels.length > 0">
      <h4 class="section-title">
        <i class="icon">📁</i>
        分類
        <span class="count">({{ categoryChannels.length }})</span>
      </h4>
      <div class="channels">
        <div 
          v-for="category in categoryChannels" 
          :key="category.id" 
          class="channel-item category-channel"
        >
          <span class="channel-icon">📁</span>
          <span class="channel-name">{{ category.name }}</span>
          <span class="category-info">
            {{ getChannelsInCategory(category.id).length }} 個頻道
          </span>
        </div>
      </div>
    </div>

    <div v-if="channels.length === 0" class="no-channels">
      <i class="icon">📭</i>
      <p>沒有找到頻道</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChannelList',
  props: {
    channels: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    textChannels() {
      return this.channels.filter(channel => channel.type === 0);
    },
    voiceChannels() {
      return this.channels.filter(channel => channel.type === 2);
    },
    categoryChannels() {
      return this.channels.filter(channel => channel.type === 4);
    }
  },
  methods: {
    getChannelsInCategory(categoryId) {
      return this.channels.filter(channel => channel.parent_id === categoryId);
    }
  }
};
</script>

<style scoped>
.channel-list {
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.channel-section {
  margin-bottom: 2rem;
}

.channel-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-title .icon {
  font-size: 1.1rem;
}

.count {
  color: #666;
  font-weight: 400;
  font-size: 0.85rem;
}

.channels {
  display: grid;
  gap: 0.5rem;
}

.channel-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  border-left: 3px solid transparent;
}

.channel-item:hover {
  background-color: #f8fafc;
}

.text-channel {
  border-left-color: #10b981;
}

.voice-channel {
  border-left-color: #f59e0b;
}

.category-channel {
  border-left-color: #8b5cf6;
}

.channel-icon {
  font-size: 1rem;
  color: #666;
  flex-shrink: 0;
}

.channel-name {
  font-weight: 500;
  color: #333;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.nsfw-badge {
  background-color: #ef4444;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.channel-topic {
  color: #666;
  font-size: 0.8rem;
  font-style: italic;
  flex: 1;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-limit,
.bitrate,
.category-info {
  color: #666;
  font-size: 0.8rem;
  background-color: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.no-channels {
  text-align: center;
  padding: 3rem 1rem;
  color: #666;
}

.no-channels .icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.no-channels p {
  margin: 0;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .channel-list {
    padding: 1rem;
  }
  
  .channel-item {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .channel-topic,
  .user-limit,
  .bitrate,
  .category-info {
    display: none;
  }
}
</style>
