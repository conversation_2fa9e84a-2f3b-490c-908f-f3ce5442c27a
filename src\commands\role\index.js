import { EmbedBuilder, SlashCommandBuilder } from "discord.js";
import { readFileSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export const command = new SlashCommandBuilder()
    .setName("role")
    .setDescription("Role management commands")

export const action = async (ctx) => {
    await ctx.deferReply({ ephemeral: true });

    // 讀取規則文件內容
    const rules = readFileSync(join(__dirname, "rules.txt"), "utf-8");

    const embed = new EmbedBuilder()
       .setColor("#0099ff")
        .setDescription(rules)
        .setTimestamp();

    const sendMessage = await ctx.channel.send({ embeds: [embed] });
    await sendMessage.react("✅");
    await ctx.deleteReply();
}