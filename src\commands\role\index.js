import { EmbedBuilder, SlashCommandBuilder } from "discord.js";
import { readFileSync } from "fs";
import { join, dirname } from "path";
import { fileURLToPath } from "url";
import { getRoleId, getEmoji } from "../../utils/config-helper.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export const command = new SlashCommandBuilder()
    .setName("role")
    .setDescription("Role management commands")

export const action = async (ctx) => {
    await ctx.deferReply({ ephemeral: true });

    // 從配置文件獲取設定
    const roleId = getRoleId('ID_1');
    const reactionEmoji = getEmoji('REACTION_1') || '✅';

    // 檢查配置是否存在
    if (!roleId) {
        await ctx.editReply({
            content: '❌ 配置錯誤：找不到角色 ID 設定',
            ephemeral: true
        });
        return;
    }

    // 讀取規則文件內容
    const rules = readFileSync(join(__dirname, "rules.txt"), "utf-8");

    const embed = new EmbedBuilder()
       .setColor("#0099ff")
        .setDescription(rules)
        .setTimestamp()
        .setFooter({
            text: `點擊 ${reactionEmoji} 來獲得角色 | 角色 ID: ${roleId}`
        });

    const sendMessage = await ctx.channel.send({ embeds: [embed] });
    await sendMessage.react(reactionEmoji);

    // 將訊息 ID 保存到配置中（可選）
    console.log(`📝 角色訊息已發送，訊息 ID: ${sendMessage.id}`);
    console.log(`💡 請將此 ID 添加到 config.yml 的 MESSAGE.ID_1 中`);

    await ctx.deleteReply();
}