# Discord Bot 配置文件使用指南

## 📋 概述

本 Discord Bot 使用 `src/config.yml` 文件來管理各種配置設定，包括角色 ID、訊息 ID、表情符號等。這樣可以避免將敏感資訊硬編碼在程式中，並且方便修改配置。

## 🔧 配置文件結構

### `src/config.yml`

```yaml
# Discord Bot 配置文件

# 角色配置
ROLE:
  ID_1: "1365788740534140958"  # 主要角色 ID
  # ID_2: "另一個角色ID"        # 可以添加更多角色
  # ID_3: "第三個角色ID"

# 訊息配置
MESSAGE:
  ID_1: "請替換為實際的訊息ID"   # 反應角色訊息 ID
  # ID_2: "另一個訊息ID"        # 可以添加更多訊息

# 表情符號配置
EMOJI:
  REACTION_1: "✅"             # 主要反應表情符號
  REACTION_2: "❌"             # 次要反應表情符號

# 頻道配置
CHANNEL:
  # LOG_CHANNEL: "日誌頻道ID"
  # WELCOME_CHANNEL: "歡迎頻道ID"

# Bot 設定
BOT:
  PREFIX: "!"                  # 命令前綴
  # ACTIVITY: "正在玩遊戲"
  # STATUS: "online"
```

## 🚀 如何使用

### 1. 使用路徑別名載入配置

現在支援 `@src/config.yml` 路徑別名：

```javascript
import { loadConfig } from '@core/config.js';

// 使用路徑別名載入配置
const config = loadConfig('@src/config.yml');
const roleId = config.ROLE.ID_1;
```

### 2. 基本使用方法

在任何 JavaScript 文件中導入配置：

```javascript
import { getConfig } from '@core/config.js';

// 獲取完整配置
const config = getConfig();
const roleId = config.ROLE.ID_1;
```

### 3. 使用輔助函數

推薦使用 `src/utils/config-helper.js` 中的輔助函數：

```javascript
import { getRoleId, getMessageId, getEmoji, getBotPrefix } from '@utils/config-helper.js';

// 獲取角色 ID
const roleId = getRoleId('ID_1');

// 獲取訊息 ID
const messageId = getMessageId('ID_1');

// 獲取表情符號
const emoji = getEmoji('REACTION_1');

// 獲取 Bot 前綴
const prefix = getBotPrefix();
```

### 4. 支援的路徑別名

項目中配置了以下路徑別名：

- `@src` - 指向 `src/` 目錄
- `@core` - 指向 `src/core/` 目錄
- `@utils` - 指向 `src/utils/` 目錄
- `@events` - 指向 `src/events/` 目錄
- `@commands` - 指向 `src/commands/` 目錄
- `@store` - 指向 `src/store/` 目錄
- `@config` - 直接指向 `src/config.yml` 文件

使用示例：

```javascript
// 載入配置文件
import { loadConfig } from '@core/config.js';
const config = loadConfig('@src/config.yml');

// 使用輔助函數
import { getRoleId } from '@utils/config-helper.js';
const roleId = getRoleId('ID_1');

// 導入事件
import { action } from '@events/addrole/index.js';
```

### 3. 在事件中使用

```javascript
// src/events/addrole/index.js
import { getRoleId, getMessageId, getEmoji } from '@utils/config-helper.js';

export const action = async (reaction, user) => {
  const roleId = getRoleId('ID_1');
  const messageId = getMessageId('ID_1');
  const reactionEmoji = getEmoji('REACTION_1');

  if (reaction.message.id !== messageId) return;
  if (reaction.emoji.name !== reactionEmoji) return;

  // 添加角色邏輯...
};
```

### 4. 在命令中使用

```javascript
// src/commands/role/index.js
import { getRoleId, getEmoji } from '@utils/config-helper.js';

export const action = async (ctx) => {
  const roleId = getRoleId('ID_1');
  const reactionEmoji = getEmoji('REACTION_1');

  if (!roleId) {
    await ctx.reply('❌ 配置錯誤：找不到角色 ID');
    return;
  }

  // 命令邏輯...
};
```

## 🛠️ 配置管理命令

Bot 提供了 `/config` 命令來管理配置：

- `/config show` - 顯示當前配置
- `/config validate` - 驗證配置文件
- `/config reload` - 重新載入配置文件

## 📝 設定步驟

### 1. 設定角色 ID

1. 在 Discord 中右鍵點擊角色
2. 選擇「複製 ID」（需要開啟開發者模式）
3. 將 ID 貼到 `config.yml` 的 `ROLE.ID_1` 中

### 2. 設定訊息 ID

1. 使用 `/role` 命令發送角色訊息
2. 複製控制台中顯示的訊息 ID
3. 將 ID 貼到 `config.yml` 的 `MESSAGE.ID_1` 中

### 3. 自定義表情符號

在 `EMOJI` 區段中設定您想要的表情符號。

## ⚠️ 注意事項

1. **ID 格式**：所有 Discord ID 都應該用引號包圍，例如 `"1234567890"`
2. **YAML 語法**：注意 YAML 的縮排和語法規則
3. **重新載入**：修改配置後使用 `/config reload` 命令重新載入
4. **權限**：確保 Bot 有足夠的權限來執行相關操作

## 🔍 故障排除

### 配置驗證失敗

使用 `/config validate` 命令檢查配置問題：

```text
❌ 配置錯誤:
   - 缺少 ROLE.ID_1 配置

⚠️ 配置警告:
   - 缺少 MESSAGE.ID_1 配置，某些功能可能無法正常運作
```

### 常見問題

1. **角色無法添加**：檢查角色 ID 是否正確
2. **反應無效**：檢查訊息 ID 和表情符號配置
3. **配置不生效**：使用 `/config reload` 重新載入配置

## 📚 API 參考

### 配置輔助函數

- `getRoleId(key)` - 獲取角色 ID
- `getMessageId(key)` - 獲取訊息 ID
- `getEmoji(key)` - 獲取表情符號
- `getChannelId(key)` - 獲取頻道 ID
- `getBotSetting(key)` - 獲取 Bot 設定
- `getBotPrefix()` - 獲取 Bot 前綴
- `validateConfig()` - 驗證配置
- `printConfigSummary()` - 打印配置摘要

### 核心配置函數

- `loadConfig(filePath)` - 載入配置文件
- `getConfig()` - 獲取當前配置

這樣您就可以輕鬆管理 Discord Bot 的所有配置了！🎉
